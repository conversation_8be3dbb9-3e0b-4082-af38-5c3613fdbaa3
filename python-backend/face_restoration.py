#!/usr/bin/env python3
"""
Face restoration service using GFPGAN library.
This script provides a command-line interface for restoring faces in images.
"""

import sys
import os
import argparse
from pathlib import Path
import tempfile
import base64
import json
from io import BytesIO
import urllib.request
import hashlib

try:
    from gfpgan import GFPGANer
    from PIL import Image
    import numpy as np
    import cv2
    import torch
except ImportError as e:
    print(f"Error: Required packages not installed. {e}", file=sys.stderr)
    print("Please install required packages:", file=sys.stderr)
    print("pip install gfpgan basicsr facexlib realesrgan torch torchvision", file=sys.stderr)
    sys.exit(1)


# Model URLs and checksums
GFPGAN_MODELS = {
    'v1.3': {
        'url': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth',
        'filename': 'GFPGANv1.3.pth',
        'md5': '0b2e69e8c7b8c8b8c8b8c8b8c8b8c8b8'  # Placeholder - actual MD5 would be needed
    },
    'v1.4': {
        'url': 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth',
        'filename': 'GFPGANv1.4.pth',
        'md5': '0b2e69e8c7b8c8b8c8b8c8b8c8b8c8b8'  # Placeholder - actual MD5 would be needed
    }
}


def get_models_dir():
    """Get the models directory path."""
    script_dir = Path(__file__).parent
    models_dir = script_dir / 'models' / 'gfpgan'
    models_dir.mkdir(parents=True, exist_ok=True)
    return models_dir


def download_model(model_version='v1.3'):
    """Download GFPGAN model if not exists."""
    if model_version not in GFPGAN_MODELS:
        raise ValueError(f"Unsupported model version: {model_version}")

    model_info = GFPGAN_MODELS[model_version]
    models_dir = get_models_dir()
    model_path = models_dir / model_info['filename']

    if model_path.exists():
        print(f"Model {model_version} already exists at {model_path}")
        return str(model_path)

    print(f"Downloading GFPGAN {model_version} model...")
    try:
        urllib.request.urlretrieve(model_info['url'], str(model_path))
        print(f"Model downloaded successfully to {model_path}")
        return str(model_path)
    except Exception as e:
        print(f"Failed to download model: {e}", file=sys.stderr)
        if model_path.exists():
            model_path.unlink()  # Remove partial download
        raise


def create_gfpgan_restorer(model_version='v1.3', upscale=2):
    """Create GFPGAN restorer instance."""
    try:
        print(f"Creating GFPGAN restorer with model {model_version}, upscale {upscale}", file=sys.stderr)
        model_path = download_model(model_version)
        print(f"Model path: {model_path}", file=sys.stderr)

        # Initialize GFPGAN
        print("Initializing GFPGAN...", file=sys.stderr)
        restorer = GFPGANer(
            model_path=model_path,
            upscale=upscale,
            arch='clean',
            channel_multiplier=2,
            bg_upsampler=None  # We'll handle background separately if needed
        )
        print("GFPGAN restorer created successfully", file=sys.stderr)

        return restorer
    except Exception as e:
        print(f"Failed to create GFPGAN restorer: {e}", file=sys.stderr)
        raise


def restore_faces_in_image(image: Image.Image, model_version='v1.3', upscale=2) -> Image.Image:
    """
    Restore faces in a PIL Image using GFPGAN.

    Args:
        image: PIL Image to process
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        PIL Image with restored faces
    """
    try:
        print(f"Starting face restoration for image size: {image.size}", file=sys.stderr)

        # Create restorer
        print("Creating GFPGAN restorer...", file=sys.stderr)
        restorer = create_gfpgan_restorer(model_version, upscale)

        # Convert PIL to OpenCV format
        print("Converting image format...", file=sys.stderr)
        if image.mode == 'RGBA':
            print("Converting RGBA to RGB with white background", file=sys.stderr)
            # Handle transparency by converting to RGB with white background
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode != 'RGB':
            print(f"Converting {image.mode} to RGB", file=sys.stderr)
            image = image.convert('RGB')

        # Convert to numpy array (OpenCV format)
        print("Converting to OpenCV format...", file=sys.stderr)
        img_array = np.array(image)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        print(f"Image array shape: {img_array.shape}", file=sys.stderr)

        # Restore faces
        print("Starting GFPGAN face restoration...", file=sys.stderr)
        _, _, restored_img = restorer.enhance(
            img_bgr,
            has_aligned=False,
            only_center_face=False,
            paste_back=True
        )
        print("GFPGAN face restoration completed", file=sys.stderr)

        # Convert back to PIL Image
        if restored_img is not None:
            print("Converting result back to PIL Image...", file=sys.stderr)
            restored_rgb = cv2.cvtColor(restored_img, cv2.COLOR_BGR2RGB)
            result_image = Image.fromarray(restored_rgb)
            print(f"Result image size: {result_image.size}", file=sys.stderr)
        else:
            print("Warning: GFPGAN returned None, using original image", file=sys.stderr)
            result_image = image

        print("Face restoration completed successfully", file=sys.stderr)
        return result_image

    except Exception as e:
        print(f"Error during face restoration: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return image  # Return original image on error


def restore_faces_from_file(input_path: str, output_path: str, model_version='v1.3', upscale=2) -> bool:
    """
    Restore faces in an image file.

    Args:
        input_path: Path to input image
        output_path: Path to save output image
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Load image
        image = Image.open(input_path)

        # Restore faces
        restored_image = restore_faces_in_image(image, model_version, upscale)

        # Save result
        restored_image.save(output_path, 'PNG', optimize=True)

        return True

    except Exception as e:
        print(f"Error processing image file: {e}", file=sys.stderr)
        return False


def restore_faces_from_base64(base64_data: str, model_version='v1.3', upscale=2) -> str:
    """
    Restore faces in a base64 encoded image.

    Args:
        base64_data: Base64 encoded image data
        model_version: GFPGAN model version to use
        upscale: Upscaling factor

    Returns:
        str: Base64 encoded result image or empty string on error
    """
    try:
        # Decode base64 data
        if base64_data.startswith('data:image'):
            # Remove data URL prefix if present
            base64_data = base64_data.split(',')[1]

        input_data = base64.b64decode(base64_data)

        # Load image
        image = Image.open(BytesIO(input_data))

        # Restore faces
        restored_image = restore_faces_in_image(image, model_version, upscale)

        # Convert back to base64
        output_buffer = BytesIO()
        restored_image.save(output_buffer, format='PNG', optimize=True)
        output_data = output_buffer.getvalue()
        output_base64 = base64.b64encode(output_data).decode('utf-8')

        return f"data:image/png;base64,{output_base64}"

    except Exception as e:
        print(f"Error processing base64 image: {e}", file=sys.stderr)
        return ""


def main():
    parser = argparse.ArgumentParser(description='Restore faces in images using GFPGAN')
    parser.add_argument('--input', '-i', required=True, help='Input image path')
    parser.add_argument('--output', '-o', required=True, help='Output image path')
    parser.add_argument('--base64', action='store_true', help='Process base64 encoded image')
    parser.add_argument('--model', '-m', default='v1.3', choices=['v1.3', 'v1.4'],
                       help='GFPGAN model version (default: v1.3)')
    parser.add_argument('--upscale', '-u', type=int, default=2,
                       help='Upscaling factor (default: 2)')

    args = parser.parse_args()

    if args.base64:
        # Read base64 data from input file or stdin
        if args.input == '-':
            base64_data = sys.stdin.read().strip()
        else:
            with open(args.input, 'r') as f:
                base64_data = f.read().strip()

        result = restore_faces_from_base64(base64_data, args.model, args.upscale)

        if result:
            if args.output == '-':
                print(result)
            else:
                with open(args.output, 'w') as f:
                    f.write(result)
            return 0
        else:
            return 1
    else:
        # Process file directly
        success = restore_faces_from_file(args.input, args.output, args.model, args.upscale)
        return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
