
# This file was generated by 'versioneer.py' (0.21) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-08-26T17:41:00-0300",
 "dirty": false,
 "error": null,
 "full-revisionid": "95b81143c9a1d760c892ffa7f406f055fc244b81",
 "version": "2.0.59"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
