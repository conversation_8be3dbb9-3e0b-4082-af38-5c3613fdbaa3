#pragma once
// @generated by torchgen/gen.py from DispatchKeyFunctions_inl.h

// NB: The implementing C++ file is RegisterDispatchKey.cpp

// The only #includes we need are for custom classes that have defaults in the C++ API
#include <c10/core/MemoryFormat.h>
#include <c10/core/Scalar.h>
#include <ATen/core/Reduction.h>

#if defined(AT_PER_OPERATOR_HEADERS) && defined(TORCH_ASSERT_ONLY_METHOD_OPERATORS)
#error This change adds a dependency on all pytorch operators, meaning the     \
  file will need to be re-compiled every time an operator is changed or added. \
  Consider including a specific operator from                                  \
  <ATen/ops/{my_operator}_mps_dispatch.h>.                   \
  See NOTE [TORCH_ASSERT_ONLY_METHOD_OPERATORS].
#endif

#include <ATen/ops/_adaptive_avg_pool2d_mps_dispatch.h>
#include <ATen/ops/_adaptive_avg_pool2d_backward_mps_dispatch.h>
#include <ATen/ops/_copy_from_mps_dispatch.h>
#include <ATen/ops/_copy_from_and_resize_mps_dispatch.h>
#include <ATen/ops/_index_put_impl_mps_dispatch.h>
#include <ATen/ops/_local_scalar_dense_mps_dispatch.h>
#include <ATen/ops/_log_softmax_mps_dispatch.h>
#include <ATen/ops/_log_softmax_backward_data_mps_dispatch.h>
#include <ATen/ops/_lstm_mps_mps_dispatch.h>
#include <ATen/ops/_mps_convolution_mps_dispatch.h>
#include <ATen/ops/_mps_convolution_transpose_mps_dispatch.h>
#include <ATen/ops/_mps_max_pool2d_mps_dispatch.h>
#include <ATen/ops/_pin_memory_mps_dispatch.h>
#include <ATen/ops/_reshape_alias_mps_dispatch.h>
#include <ATen/ops/_softmax_mps_dispatch.h>
#include <ATen/ops/_softmax_backward_data_mps_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact2d_mps_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact2d_backward_mps_dispatch.h>
#include <ATen/ops/abs_mps_dispatch.h>
#include <ATen/ops/acos_mps_dispatch.h>
#include <ATen/ops/acosh_mps_dispatch.h>
#include <ATen/ops/adaptive_avg_pool2d_mps_dispatch.h>
#include <ATen/ops/adaptive_max_pool2d_mps_dispatch.h>
#include <ATen/ops/adaptive_max_pool2d_backward_mps_dispatch.h>
#include <ATen/ops/add_mps_dispatch.h>
#include <ATen/ops/addbmm_mps_dispatch.h>
#include <ATen/ops/addcdiv_mps_dispatch.h>
#include <ATen/ops/addcmul_mps_dispatch.h>
#include <ATen/ops/addmm_mps_dispatch.h>
#include <ATen/ops/addmv_mps_dispatch.h>
#include <ATen/ops/all_mps_dispatch.h>
#include <ATen/ops/amax_mps_dispatch.h>
#include <ATen/ops/amin_mps_dispatch.h>
#include <ATen/ops/any_mps_dispatch.h>
#include <ATen/ops/arange_mps_dispatch.h>
#include <ATen/ops/argmax_mps_dispatch.h>
#include <ATen/ops/argmin_mps_dispatch.h>
#include <ATen/ops/as_strided_mps_dispatch.h>
#include <ATen/ops/asin_mps_dispatch.h>
#include <ATen/ops/asinh_mps_dispatch.h>
#include <ATen/ops/atan_mps_dispatch.h>
#include <ATen/ops/atan2_mps_dispatch.h>
#include <ATen/ops/atanh_mps_dispatch.h>
#include <ATen/ops/avg_pool2d_mps_dispatch.h>
#include <ATen/ops/avg_pool2d_backward_mps_dispatch.h>
#include <ATen/ops/baddbmm_mps_dispatch.h>
#include <ATen/ops/bernoulli_mps_dispatch.h>
#include <ATen/ops/binary_cross_entropy_mps_dispatch.h>
#include <ATen/ops/binary_cross_entropy_backward_mps_dispatch.h>
#include <ATen/ops/bmm_mps_dispatch.h>
#include <ATen/ops/cat_mps_dispatch.h>
#include <ATen/ops/ceil_mps_dispatch.h>
#include <ATen/ops/clamp_mps_dispatch.h>
#include <ATen/ops/clamp_max_mps_dispatch.h>
#include <ATen/ops/clamp_min_mps_dispatch.h>
#include <ATen/ops/constant_pad_nd_mps_dispatch.h>
#include <ATen/ops/cos_mps_dispatch.h>
#include <ATen/ops/cosh_mps_dispatch.h>
#include <ATen/ops/count_nonzero_mps_dispatch.h>
#include <ATen/ops/diag_mps_dispatch.h>
#include <ATen/ops/div_mps_dispatch.h>
#include <ATen/ops/dot_mps_dispatch.h>
#include <ATen/ops/elu_mps_dispatch.h>
#include <ATen/ops/elu_backward_mps_dispatch.h>
#include <ATen/ops/embedding_dense_backward_mps_dispatch.h>
#include <ATen/ops/empty_mps_dispatch.h>
#include <ATen/ops/empty_strided_mps_dispatch.h>
#include <ATen/ops/eq_mps_dispatch.h>
#include <ATen/ops/equal_mps_dispatch.h>
#include <ATen/ops/erf_mps_dispatch.h>
#include <ATen/ops/exp_mps_dispatch.h>
#include <ATen/ops/exp2_mps_dispatch.h>
#include <ATen/ops/exponential_mps_dispatch.h>
#include <ATen/ops/eye_mps_dispatch.h>
#include <ATen/ops/fill_mps_dispatch.h>
#include <ATen/ops/flip_mps_dispatch.h>
#include <ATen/ops/floor_mps_dispatch.h>
#include <ATen/ops/gather_mps_dispatch.h>
#include <ATen/ops/ge_mps_dispatch.h>
#include <ATen/ops/gelu_mps_dispatch.h>
#include <ATen/ops/gelu_backward_mps_dispatch.h>
#include <ATen/ops/glu_mps_dispatch.h>
#include <ATen/ops/glu_backward_mps_dispatch.h>
#include <ATen/ops/gt_mps_dispatch.h>
#include <ATen/ops/hardtanh_mps_dispatch.h>
#include <ATen/ops/hardtanh_backward_mps_dispatch.h>
#include <ATen/ops/huber_loss_mps_dispatch.h>
#include <ATen/ops/huber_loss_backward_mps_dispatch.h>
#include <ATen/ops/index_mps_dispatch.h>
#include <ATen/ops/index_add_mps_dispatch.h>
#include <ATen/ops/index_select_mps_dispatch.h>
#include <ATen/ops/is_pinned_mps_dispatch.h>
#include <ATen/ops/is_set_to_mps_dispatch.h>
#include <ATen/ops/isnan_mps_dispatch.h>
#include <ATen/ops/le_mps_dispatch.h>
#include <ATen/ops/leaky_relu_mps_dispatch.h>
#include <ATen/ops/leaky_relu_backward_mps_dispatch.h>
#include <ATen/ops/linear_mps_dispatch.h>
#include <ATen/ops/linear_backward_mps_dispatch.h>
#include <ATen/ops/linspace_mps_dispatch.h>
#include <ATen/ops/log_mps_dispatch.h>
#include <ATen/ops/log10_mps_dispatch.h>
#include <ATen/ops/log1p_mps_dispatch.h>
#include <ATen/ops/log2_mps_dispatch.h>
#include <ATen/ops/logaddexp_mps_dispatch.h>
#include <ATen/ops/logaddexp2_mps_dispatch.h>
#include <ATen/ops/logical_and_mps_dispatch.h>
#include <ATen/ops/logical_not_mps_dispatch.h>
#include <ATen/ops/logical_or_mps_dispatch.h>
#include <ATen/ops/logical_xor_mps_dispatch.h>
#include <ATen/ops/lstm_mps_backward_mps_dispatch.h>
#include <ATen/ops/lt_mps_dispatch.h>
#include <ATen/ops/masked_fill_mps_dispatch.h>
#include <ATen/ops/masked_select_mps_dispatch.h>
#include <ATen/ops/max_mps_dispatch.h>
#include <ATen/ops/max_pool2d_with_indices_mps_dispatch.h>
#include <ATen/ops/max_pool2d_with_indices_backward_mps_dispatch.h>
#include <ATen/ops/maximum_mps_dispatch.h>
#include <ATen/ops/mean_mps_dispatch.h>
#include <ATen/ops/min_mps_dispatch.h>
#include <ATen/ops/minimum_mps_dispatch.h>
#include <ATen/ops/mm_mps_dispatch.h>
#include <ATen/ops/mps_convolution_backward_mps_dispatch.h>
#include <ATen/ops/mps_convolution_transpose_backward_mps_dispatch.h>
#include <ATen/ops/mps_max_pool2d_backward_mps_dispatch.h>
#include <ATen/ops/mse_loss_mps_dispatch.h>
#include <ATen/ops/mse_loss_backward_mps_dispatch.h>
#include <ATen/ops/mul_mps_dispatch.h>
#include <ATen/ops/multinomial_mps_dispatch.h>
#include <ATen/ops/native_batch_norm_mps_dispatch.h>
#include <ATen/ops/native_batch_norm_backward_mps_dispatch.h>
#include <ATen/ops/native_layer_norm_mps_dispatch.h>
#include <ATen/ops/native_layer_norm_backward_mps_dispatch.h>
#include <ATen/ops/ne_mps_dispatch.h>
#include <ATen/ops/neg_mps_dispatch.h>
#include <ATen/ops/nll_loss2d_backward_mps_dispatch.h>
#include <ATen/ops/nll_loss2d_forward_mps_dispatch.h>
#include <ATen/ops/nll_loss_backward_mps_dispatch.h>
#include <ATen/ops/nll_loss_forward_mps_dispatch.h>
#include <ATen/ops/norm_mps_dispatch.h>
#include <ATen/ops/normal_mps_dispatch.h>
#include <ATen/ops/permute_mps_dispatch.h>
#include <ATen/ops/pow_mps_dispatch.h>
#include <ATen/ops/prelu_mps_dispatch.h>
#include <ATen/ops/prelu_backward_mps_dispatch.h>
#include <ATen/ops/prod_mps_dispatch.h>
#include <ATen/ops/put_mps_dispatch.h>
#include <ATen/ops/random_mps_dispatch.h>
#include <ATen/ops/reciprocal_mps_dispatch.h>
#include <ATen/ops/reflection_pad1d_mps_dispatch.h>
#include <ATen/ops/reflection_pad1d_backward_mps_dispatch.h>
#include <ATen/ops/reflection_pad2d_mps_dispatch.h>
#include <ATen/ops/reflection_pad2d_backward_mps_dispatch.h>
#include <ATen/ops/reflection_pad3d_mps_dispatch.h>
#include <ATen/ops/reflection_pad3d_backward_mps_dispatch.h>
#include <ATen/ops/relu_mps_dispatch.h>
#include <ATen/ops/repeat_mps_dispatch.h>
#include <ATen/ops/replication_pad1d_mps_dispatch.h>
#include <ATen/ops/replication_pad1d_backward_mps_dispatch.h>
#include <ATen/ops/replication_pad2d_mps_dispatch.h>
#include <ATen/ops/replication_pad2d_backward_mps_dispatch.h>
#include <ATen/ops/replication_pad3d_mps_dispatch.h>
#include <ATen/ops/replication_pad3d_backward_mps_dispatch.h>
#include <ATen/ops/resize_mps_dispatch.h>
#include <ATen/ops/round_mps_dispatch.h>
#include <ATen/ops/rsqrt_mps_dispatch.h>
#include <ATen/ops/scatter_mps_dispatch.h>
#include <ATen/ops/scatter_add_mps_dispatch.h>
#include <ATen/ops/set_mps_dispatch.h>
#include <ATen/ops/sigmoid_mps_dispatch.h>
#include <ATen/ops/sigmoid_backward_mps_dispatch.h>
#include <ATen/ops/sign_mps_dispatch.h>
#include <ATen/ops/silu_mps_dispatch.h>
#include <ATen/ops/silu_backward_mps_dispatch.h>
#include <ATen/ops/sin_mps_dispatch.h>
#include <ATen/ops/sinh_mps_dispatch.h>
#include <ATen/ops/smooth_l1_loss_mps_dispatch.h>
#include <ATen/ops/smooth_l1_loss_backward_mps_dispatch.h>
#include <ATen/ops/softplus_mps_dispatch.h>
#include <ATen/ops/softplus_backward_mps_dispatch.h>
#include <ATen/ops/sqrt_mps_dispatch.h>
#include <ATen/ops/std_mps_dispatch.h>
#include <ATen/ops/sub_mps_dispatch.h>
#include <ATen/ops/sum_mps_dispatch.h>
#include <ATen/ops/tan_mps_dispatch.h>
#include <ATen/ops/tanh_mps_dispatch.h>
#include <ATen/ops/tanh_backward_mps_dispatch.h>
#include <ATen/ops/threshold_mps_dispatch.h>
#include <ATen/ops/threshold_backward_mps_dispatch.h>
#include <ATen/ops/topk_mps_dispatch.h>
#include <ATen/ops/tril_mps_dispatch.h>
#include <ATen/ops/triu_mps_dispatch.h>
#include <ATen/ops/trunc_mps_dispatch.h>
#include <ATen/ops/uniform_mps_dispatch.h>
#include <ATen/ops/upsample_bilinear2d_mps_dispatch.h>
#include <ATen/ops/upsample_bilinear2d_backward_mps_dispatch.h>
#include <ATen/ops/upsample_nearest1d_mps_dispatch.h>
#include <ATen/ops/upsample_nearest2d_mps_dispatch.h>
#include <ATen/ops/upsample_nearest2d_backward_mps_dispatch.h>
#include <ATen/ops/var_mps_dispatch.h>
#include <ATen/ops/view_mps_dispatch.h>
#include <ATen/ops/view_as_real_mps_dispatch.h>
#include <ATen/ops/where_mps_dispatch.h>
#include <ATen/ops/zero_mps_dispatch.h>



