#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API value_selecting_reduction_backward {
  using schema = at::Tensor (const at::Tensor &, int64_t, const at::Tensor &, c10::SymIntArrayRef, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::value_selecting_reduction_backward";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "value_selecting_reduction_backward(Tensor grad, int dim, Tensor indices, SymInt[] sizes, bool keepdim) -> Tensor";
  static at::Tensor call(const at::Tensor & grad, int64_t dim, const at::Tensor & indices, c10::SymIntArrayRef sizes, bool keepdim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & grad, int64_t dim, const at::Tensor & indices, c10::SymIntArrayRef sizes, bool keepdim);
};

}} // namespace at::_ops
