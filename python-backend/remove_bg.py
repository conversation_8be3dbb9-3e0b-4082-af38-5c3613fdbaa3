#!/usr/bin/env python3
"""
Background removal service using rembg library.
This script provides a command-line interface for removing backgrounds from images.
"""

import sys
import os
import argparse
from pathlib import Path
import tempfile
import base64
import json
from io import BytesIO

try:
    from rembg import remove
    from PIL import Image, ImageFilter
    import numpy as np
    import cv2
except ImportError as e:
    print(f"Error: Required packages not installed. {e}", file=sys.stderr)
    print("Please install required packages:", file=sys.stderr)
    print("pip install rembg pillow opencv-python numpy", file=sys.stderr)
    sys.exit(1)


def improve_edges(image: Image.Image) -> Image.Image:
    """
    Improve the edges of a background-removed image to reduce thick borders.

    Args:
        image: PIL Image with transparent background

    Returns:
        PIL Image with improved edges
    """
    try:
        # Convert to RGBA if not already
        if image.mode != 'RGBA':
            image = image.convert('RGBA')

        # Convert to numpy array for processing
        img_array = np.array(image)

        # Extract alpha channel
        alpha = img_array[:, :, 3]

        # Apply morphological operations to smooth the alpha mask
        # Erosion followed by dilation (opening) to remove small artifacts
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        alpha_smooth = cv2.morphologyEx(alpha, cv2.MORPH_OPEN, kernel)

        # Apply Gaussian blur to soften edges
        alpha_smooth = cv2.GaussianBlur(alpha_smooth, (3, 3), 0.5)

        # Apply median filter to reduce noise while preserving edges
        alpha_smooth = cv2.medianBlur(alpha_smooth, 3)

        # Create a gradient mask for edge feathering
        # Find edges using Canny edge detection
        edges = cv2.Canny(alpha_smooth, 50, 150)

        # Dilate edges to create a feathering zone
        edge_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        edge_zone = cv2.dilate(edges, edge_kernel, iterations=2)

        # Apply additional blur to the edge zone
        alpha_feathered = alpha_smooth.copy().astype(np.float32)
        edge_mask = edge_zone > 0

        # Soften the edges by reducing alpha in the edge zone
        alpha_feathered[edge_mask] = alpha_feathered[edge_mask] * 0.8

        # Apply a final gentle blur to the entire alpha channel
        alpha_feathered = cv2.GaussianBlur(alpha_feathered, (1, 1), 0.3)

        # Ensure alpha values are in valid range
        alpha_feathered = np.clip(alpha_feathered, 0, 255).astype(np.uint8)

        # Replace the alpha channel in the original image
        img_array[:, :, 3] = alpha_feathered

        # Convert back to PIL Image
        improved_image = Image.fromarray(img_array, 'RGBA')

        return improved_image

    except Exception as e:
        print(f"Warning: Edge improvement failed, using original: {e}", file=sys.stderr)
        return image


def remove_background_from_file(input_path: str, output_path: str) -> bool:
    """
    Remove background from an image file.

    Args:
        input_path: Path to input image
        output_path: Path to save output image

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Read input image
        with open(input_path, 'rb') as input_file:
            input_data = input_file.read()

        # Remove background
        output_data = remove(input_data)

        # Load the result as PIL Image for post-processing
        result_image = Image.open(BytesIO(output_data))

        # Improve edges to reduce thick borders
        improved_image = improve_edges(result_image)

        # Save the improved image
        improved_image.save(output_path, 'PNG', optimize=True)

        return True

    except Exception as e:
        print(f"Error processing image: {e}", file=sys.stderr)
        return False


def remove_background_from_base64(base64_data: str) -> str:
    """
    Remove background from a base64 encoded image.

    Args:
        base64_data: Base64 encoded image data

    Returns:
        str: Base64 encoded result image or empty string on error
    """
    try:
        # Decode base64 data
        if base64_data.startswith('data:image'):
            # Remove data URL prefix if present
            base64_data = base64_data.split(',')[1]

        input_data = base64.b64decode(base64_data)

        # Remove background
        output_data = remove(input_data)

        # Load the result as PIL Image for post-processing
        result_image = Image.open(BytesIO(output_data))

        # Improve edges to reduce thick borders
        improved_image = improve_edges(result_image)

        # Convert improved image back to base64
        output_buffer = BytesIO()
        improved_image.save(output_buffer, format='PNG', optimize=True)
        output_data = output_buffer.getvalue()
        output_base64 = base64.b64encode(output_data).decode('utf-8')

        return f"data:image/png;base64,{output_base64}"

    except Exception as e:
        print(f"Error processing base64 image: {e}", file=sys.stderr)
        return ""


def main():
    parser = argparse.ArgumentParser(description='Remove background from images using rembg')
    parser.add_argument('--input', '-i', required=True, help='Input image path')
    parser.add_argument('--output', '-o', required=True, help='Output image path')
    parser.add_argument('--base64', action='store_true', help='Process base64 encoded image')

    args = parser.parse_args()

    if args.base64:
        # Read base64 data from input file or stdin
        if args.input == '-':
            base64_data = sys.stdin.read().strip()
        else:
            with open(args.input, 'r') as f:
                base64_data = f.read().strip()

        result = remove_background_from_base64(base64_data)

        if result:
            if args.output == '-':
                print(result)
            else:
                with open(args.output, 'w') as f:
                    f.write(result)
            return 0
        else:
            return 1
    else:
        # Process file directly
        success = remove_background_from_file(args.input, args.output)
        return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
