import { useState } from "react";
import { TbUpload, TbDownload, Tb<PERSON><PERSON><PERSON>, TbArrowUp, TbBackground, Tb<PERSON><PERSON>let, TbLoader2, TbUser } from "react-icons/tb";
import { css, cx } from "styled-system/css";
import { hstack, grid } from "styled-system/patterns";
import { invoke } from "@tauri-apps/api/core";
import { save } from "@tauri-apps/plugin-dialog";
import { writeFile } from "@tauri-apps/plugin-fs";

const containerStyle = css({
	padding: "24px",
	backgroundColor: "#f9fafb",
	minHeight: "100vh",
});

const headerStyle = css({
});

const titleStyle = css({
	fontSize: "24px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "4px",
});

const mainContentStyle = grid({
	columns: 2,
	gap: "24px",
	marginBottom: "32px",
});

const cardStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	overflow: "hidden",
});

const cardHeaderStyle = css({
	padding: "16px 20px",
	borderBottom: "1px solid #e5e7eb",
	fontSize: "16px",
	fontWeight: "600",
	color: "#111827",
});

const uploadAreaStyle = css({
	padding: "40px 20px",
	textAlign: "center",
	border: "2px dashed #d1d5db",
	margin: "20px",
	borderRadius: "8px",
	backgroundColor: "#f9fafb",
	cursor: "pointer",
	transition: "all 0.2s",
	"&:hover": {
		borderColor: "#6366f1",
		backgroundColor: "#f3f4f6",
	},
});

const uploadedImageStyle = css({
	padding: "20px",
	textAlign: "center",
});

const imageStyle = css({
	maxWidth: "100%",
	maxHeight: "300px",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
});

const transparentImageStyle = css({
	maxWidth: "100%",
	maxHeight: "300px",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	background: `
		linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
		linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
	`,
	backgroundSize: "20px 20px",
	backgroundPosition: "0 0, 0 10px, 10px -10px, -10px 0px",
});

const uploadIconStyle = css({
	color: "#6b7280",
	marginBottom: "12px",
});

const uploadTextStyle = css({
	fontSize: "16px",
	fontWeight: "500",
	color: "#374151",
	marginBottom: "4px",
});

const uploadSubtextStyle = css({
	fontSize: "14px",
	color: "#6b7280",
});

const resultPlaceholderStyle = css({
	padding: "80px 20px",
	textAlign: "center",
	color: "#9ca3af",
});

const toolsHeaderStyle = css({
	fontSize: "18px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "16px",
});

const toolsGridStyle = grid({
	columns: 2,
	gap: "16px",
});

const toolCardStyle = css({
	backgroundColor: "white",
	borderRadius: "8px",
	border: "1px solid #e5e7eb",
	padding: "20px",
	cursor: "pointer",
	transition: "all 0.2s",
	"&:hover": {
		borderColor: "#6366f1",
		transform: "translateY(-2px)",
		boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
	},
});

const toolTitleStyle = css({
	fontSize: "16px",
	fontWeight: "600",
	color: "#111827",
	marginBottom: "4px",
});

const toolDescriptionStyle = css({
	fontSize: "14px",
	color: "#6b7280",
});

const successMessageStyle = css({
	backgroundColor: "#d1fae5",
	color: "#065f46",
	padding: "12px 16px",
	borderRadius: "6px",
	fontSize: "14px",
	margin: "16px 20px",
});



interface ProcessingResult {
	success: boolean;
	message: string;
	output_data?: string;
}

// Helper function to check if we're running in Tauri context
const isTauriContext = () => {
	// For Tauri v2, check multiple indicators
	return typeof window !== 'undefined' && (
		(window as any).__TAURI__ !== undefined ||
		(window as any).__TAURI_INTERNALS__ !== undefined ||
		typeof invoke !== 'undefined'
	);
};

const ImageProcessingStudio = () => {
	const [uploadedImage, setUploadedImage] = useState<string | null>(null);
	const [isUploaded, setIsUploaded] = useState(false);
	const [processedImage, setProcessedImage] = useState<string | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [processingMessage, setProcessingMessage] = useState<string>("");
	const [selectedTool, setSelectedTool] = useState<string | null>(null);

	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setUploadedImage(e.target?.result as string);
				setIsUploaded(true);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		const file = event.dataTransfer.files[0];
		if (file && file.type.startsWith('image/')) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setUploadedImage(e.target?.result as string);
				setIsUploaded(true);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
	};

	const handleRemoveBackground = async () => {
		if (!uploadedImage) {
			setProcessingMessage("Please upload an image first");
			return;
		}

		setIsProcessing(true);
		setSelectedTool("remove-background");
		setProcessingMessage("Removing background...");
		setProcessedImage(null);

		try {
			console.log('Starting background removal...');
			console.log('Input image data length:', uploadedImage.length);
			console.log('Input image starts with:', uploadedImage.substring(0, 50));

			// Debug Tauri context
			console.log('window.__TAURI__:', (window as any).__TAURI__);
			console.log('window.__TAURI_INTERNALS__:', (window as any).__TAURI_INTERNALS__);
			console.log('typeof invoke:', typeof invoke);
			console.log('isTauriContext():', isTauriContext());
			console.log('typeof window:', typeof window);

			console.log('Attempting to call Tauri backend directly...');

			// Try to call Tauri backend directly, regardless of context detection
			try {
				const testResult = await invoke<string>("test_connection");
				console.log('Tauri connection test successful:', testResult);
			} catch (testError) {
				console.error('Tauri connection test failed:', testError);

				// If Tauri call fails, fall back to development mode
				if (!isTauriContext()) {
					setProcessingMessage("Development mode: Simulating background removal...");
					await new Promise(resolve => setTimeout(resolve, 2000));
					setProcessedImage(uploadedImage);
					setProcessingMessage("Demo mode: Background removal simulated (run 'npm run tauri dev' for real processing)");
					return;
				} else {
					setProcessingMessage("Error: Cannot connect to Tauri backend");
					return;
				}
			}

			try {
				const result = await invoke<ProcessingResult>("remove_background", {
					imageData: uploadedImage
				});

				console.log('Background removal result:', {
					success: result.success,
					message: result.message,
					hasOutputData: !!result.output_data,
					outputDataLength: result.output_data?.length || 0
				});

				if (result.success && result.output_data) {
					console.log('Output image starts with:', result.output_data.substring(0, 50));

					// Check if the output is actually different from input
					if (result.output_data === uploadedImage) {
						console.warn('Output image is identical to input image!');
						setProcessingMessage("Warning: Output appears identical to input. Background removal may have failed.");
					} else if (result.output_data.length === uploadedImage.length) {
						console.warn('Output image has same length as input - may be identical');
						setProcessingMessage("Background removed (processing complete)");
					} else {
						console.log('Images are different - background removal appears successful');
						setProcessingMessage("Background removed successfully!");
					}

					setProcessedImage(result.output_data);
				} else {
					console.error('Background removal failed:', result.message);
					setProcessingMessage(result.message || "Failed to remove background");
				}
			} catch (invokeError) {
				console.error('Tauri invoke error:', invokeError);
				throw invokeError; // Re-throw to be caught by outer catch
			}
		} catch (error) {
			console.error("Error removing background:", error);
			if (!isTauriContext()) {
				setProcessingMessage("Development mode: Please run 'npm run tauri dev' to test background removal");
			} else {
				setProcessingMessage("Error: " + String(error));
			}
		} finally {
			setIsProcessing(false);
		}
	};

	const handleFaceRestoration = async () => {
		if (!uploadedImage) {
			setProcessingMessage("Please upload an image first");
			return;
		}

		setIsProcessing(true);
		setSelectedTool("face-restoration");
		setProcessingMessage("Initializing face restoration...");
		setProcessedImage(null);

		try {
			console.log('Starting face restoration...');
			console.log('Input image data length:', uploadedImage.length);
			console.log('Input image starts with:', uploadedImage.substring(0, 50));

			// Debug Tauri context
			console.log('window.__TAURI__:', (window as any).__TAURI__);
			console.log('window.__TAURI_INTERNALS__:', (window as any).__TAURI_INTERNALS__);
			console.log('typeof invoke:', typeof invoke);
			console.log('isTauriContext():', isTauriContext());
			console.log('typeof window:', typeof window);

			console.log('Attempting to call Tauri backend directly...');

			// Try to call Tauri backend directly, regardless of context detection
			try {
				const testResult = await invoke<string>("test_connection");
				console.log('Tauri connection test successful:', testResult);
			} catch (testError) {
				console.error('Tauri connection test failed:', testError);

				// If Tauri call fails, fall back to development mode
				if (!isTauriContext()) {
					setProcessingMessage("Development mode: Simulating face restoration...");
					await new Promise(resolve => setTimeout(resolve, 2000));
					setProcessedImage(uploadedImage);
					setProcessingMessage("Demo mode: Face restoration simulated (run 'npm run tauri dev' for real processing)");
					return;
				} else {
					setProcessingMessage("Error: Cannot connect to Tauri backend");
					return;
				}
			}

			try {
				setProcessingMessage("Processing faces with GFPGAN... (this may take 1-5 minutes)");

				const result = await invoke<ProcessingResult>("restore_faces", {
					imageData: uploadedImage,
					modelVersion: "v1.3",
					upscale: 2
				});

				console.log('Face restoration result:', {
					success: result.success,
					message: result.message,
					hasOutputData: !!result.output_data,
					outputDataLength: result.output_data?.length || 0
				});

				if (result.success && result.output_data) {
					console.log('Output image starts with:', result.output_data.substring(0, 50));

					// Check if the output is actually different from input
					if (result.output_data === uploadedImage) {
						console.warn('Output image is identical to input image!');
						setProcessingMessage("Warning: Output appears identical to input. Face restoration may have failed.");
					} else if (result.output_data.length === uploadedImage.length) {
						console.warn('Output image has same length as input - may be identical');
						setProcessingMessage("Faces restored (processing complete)");
					} else {
						console.log('Images are different - face restoration appears successful');
						setProcessingMessage("Faces restored successfully!");
					}

					setProcessedImage(result.output_data);
				} else {
					console.error('Face restoration failed:', result.message);
					setProcessingMessage(result.message || "Failed to restore faces");
				}
			} catch (invokeError) {
				console.error('Tauri invoke error:', invokeError);
				throw invokeError; // Re-throw to be caught by outer catch
			}
		} catch (error) {
			console.error("Error restoring faces:", error);
			if (!isTauriContext()) {
				setProcessingMessage("Development mode: Please run 'npm run tauri dev' to test face restoration");
			} else {
				setProcessingMessage("Error: " + String(error));
			}
		} finally {
			setIsProcessing(false);
		}
	};

	const handleDownload = async () => {
		if (!processedImage) {
			console.error('No processed image to download');
			return;
		}

		console.log('Starting native save dialog...');
		console.log('Processed image starts with:', processedImage.substring(0, 50));

		try {
			// Check if we're in Tauri context
			if (!isTauriContext()) {
				console.log('Not in Tauri context, using browser download...');
				// Fallback to browser download for development
				const base64Data = processedImage.split(',')[1];
				const byteCharacters = atob(base64Data);
				const byteNumbers = new Array(byteCharacters.length);

				for (let i = 0; i < byteCharacters.length; i++) {
					byteNumbers[i] = byteCharacters.charCodeAt(i);
				}

				const byteArray = new Uint8Array(byteNumbers);
				const blob = new Blob([byteArray], { type: 'image/png' });
				const url = URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;
				link.download = selectedTool === "remove-background" ? 'image-no-bg.png' :
								selectedTool === "face-restoration" ? 'image-faces-restored.png' : 'processed-image.png';
				link.click();
				URL.revokeObjectURL(url);
				return;
			}

			// Use Tauri native save dialog
			const defaultFilename = selectedTool === "remove-background" ? 'image-no-bg.png' :
									selectedTool === "face-restoration" ? 'image-faces-restored.png' : 'processed-image.png';

			const filePath = await save({
				defaultPath: defaultFilename,
				filters: [
					{
						name: 'PNG Images',
						extensions: ['png']
					},
					{
						name: 'All Files',
						extensions: ['*']
					}
				]
			});

			if (!filePath) {
				console.log('Save dialog was cancelled');
				return;
			}

			console.log('User selected file path:', filePath);

			// Extract base64 data and convert to bytes
			const base64Data = processedImage.split(',')[1]; // Remove data:image/png;base64, prefix
			const byteCharacters = atob(base64Data);
			const byteNumbers = new Array(byteCharacters.length);

			for (let i = 0; i < byteCharacters.length; i++) {
				byteNumbers[i] = byteCharacters.charCodeAt(i);
			}

			const byteArray = new Uint8Array(byteNumbers);

			console.log('Writing file, size:', byteArray.length, 'bytes');

			// Write file using Tauri fs API
			await writeFile(filePath, byteArray);

			console.log('File saved successfully to:', filePath);

			// Show success message
			setProcessingMessage(`Image saved successfully to: ${filePath}`);

		} catch (error) {
			console.error('Save failed:', error);
			setProcessingMessage(`Save failed: ${error}`);
		}
	};

	return (
		<div className={containerStyle}>
			<div className={headerStyle}>
				{!isTauriContext() && (
					<div className={css({
						backgroundColor: "#fef3c7",
						color: "#92400e",
						padding: "8px 12px",
						borderRadius: "6px",
						fontSize: "12px",
						border: "1px solid #fbbf24"
					})}>
						⚠️ Development Mode: Run <code>npm run tauri dev</code> for full functionality
					</div>
				)}
			</div>

			<div className={mainContentStyle}>
				{/* Upload Image Section */}
				<div className={cardStyle}>
					<div className={cardHeaderStyle}>Upload Image</div>
					{isUploaded && uploadedImage ? (
						<>
							<div className={successMessageStyle}>
								Image uploaded successfully. Choose a processing tool below.
							</div>
							<div className={uploadedImageStyle}>
								<img src={uploadedImage} alt="Uploaded" className={imageStyle} />
								<div style={{ marginTop: '16px' }}>
									<button
										onClick={() => document.getElementById('file-input')?.click()}
										className={css({
											backgroundColor: "#f3f4f6",
											color: "#374151",
											padding: "8px 16px",
											borderRadius: "6px",
											border: "1px solid #d1d5db",
											cursor: "pointer",
											fontSize: "14px",
											fontWeight: "500",
											"&:hover": {
												backgroundColor: "#e5e7eb"
											}
										})}
									>
										<TbUpload size={16} style={{ marginRight: '8px', display: 'inline' }} />
										Upload New Image
									</button>
								</div>
							</div>
							<input
								id="file-input"
								type="file"
								accept="image/*"
								onChange={handleImageUpload}
								style={{ display: 'none' }}
							/>
						</>
					) : (
						<div
							className={uploadAreaStyle}
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							onClick={() => document.getElementById('file-input')?.click()}
						>
							<TbUpload size={48} className={uploadIconStyle} />
							<div className={uploadTextStyle}>Click to upload or drag and drop</div>
							<div className={uploadSubtextStyle}>PNG, JPG, GIF up to 10MB</div>
							<input
								id="file-input"
								type="file"
								accept="image/*"
								onChange={handleImageUpload}
								style={{ display: 'none' }}
							/>
						</div>
					)}
				</div>

				{/* Processed Result Section */}
				<div className={cardStyle}>
					<div className={cardHeaderStyle}>Processed Result</div>
					{isProcessing ? (
						<div className={resultPlaceholderStyle}>
							<TbLoader2 size={48} style={{ marginBottom: '12px', animation: 'spin 1s linear infinite' }} />
							<div>{processingMessage}</div>
						</div>
					) : processedImage ? (
						<>
							<div className={successMessageStyle}>
								{processingMessage}
							</div>
							{/* Show processed image */}
							<div className={uploadedImageStyle}>
								<img
									src={processedImage}
									alt="Processed"
									className={selectedTool === "remove-background" ? transparentImageStyle : imageStyle}
								/>
								{selectedTool === "remove-background" && (
									<div className={css({
										fontSize: "12px",
										color: "#6b7280",
										textAlign: "center",
										fontStyle: "italic",
										marginTop: "8px"
									})}>
										✨ Background removed - checkered pattern shows transparency
									</div>
								)}
							</div>
							<div style={{ textAlign: 'center', marginBottom: "24px" }}>
								<button
									onClick={handleDownload}
									className={css({
										backgroundColor: "#6366f1",
										color: "white",
										padding: "12px 24px",
										borderRadius: "8px",
										border: "none",
										cursor: "pointer",
										fontSize: "16px",
										fontWeight: "600",
										"&:hover": {
											backgroundColor: "#5856eb"
										}
									})}
								>
									<TbDownload size={20} style={{ marginRight: '8px', display: 'inline' }} />
									Download {selectedTool === "remove-background" ? "PNG" :
											selectedTool === "face-restoration" ? "Restored" : "Result"}
								</button>
							</div>
						</>
					) : (
						<div className={resultPlaceholderStyle}>
							<TbDownload size={48} style={{ marginBottom: '12px' }} />
							<div>Upload an image and select a tool to see results here</div>
						</div>
					)}
				</div>
			</div>

			{/* Processing Tools Section */}
			<div>
				<h2 className={toolsHeaderStyle}>Choose Processing Tool</h2>
				<div className={toolsGridStyle}>
					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#10b981", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbSparkles size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>Image Enhancement</div>
								<div className={toolDescriptionStyle}>Improve quality, brightness, and contrast</div>
							</div>
						</div>
					</div>

					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#3b82f6", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbArrowUp size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>AI Upscale</div>
								<div className={toolDescriptionStyle}>Increase resolution using AI technology</div>
							</div>
						</div>
					</div>

					<div
						className={cx(
							toolCardStyle,
							selectedTool === "remove-background" ? css({ borderColor: "#8b5cf6", backgroundColor: "#faf5ff" }) : "",
							!uploadedImage ? css({ opacity: 0.5, cursor: "not-allowed" }) : ""
						)}
						onClick={uploadedImage && !isProcessing ? handleRemoveBackground : undefined}
					>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#8b5cf6", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								{isProcessing && selectedTool === "remove-background" ? (
									<TbLoader2 size={24} className={css({ color: "white", animation: "spin 1s linear infinite" })} />
								) : (
									<TbBackground size={24} className={css({ color: "white" })} />
								)}
							</div>
							<div>
								<div className={toolTitleStyle}>Remove Background</div>
								<div className={toolDescriptionStyle}>
									{isProcessing && selectedTool === "remove-background"
										? "Processing..."
										: "Automatically remove image background"
									}
								</div>
							</div>
						</div>
					</div>

					<div
						className={cx(
							toolCardStyle,
							selectedTool === "face-restoration" ? css({ borderColor: "#ec4899", backgroundColor: "#fdf2f8" }) : "",
							!uploadedImage ? css({ opacity: 0.5, cursor: "not-allowed" }) : ""
						)}
						onClick={uploadedImage && !isProcessing ? handleFaceRestoration : undefined}
					>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#ec4899", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								{isProcessing && selectedTool === "face-restoration" ? (
									<TbLoader2 size={24} className={css({ color: "white", animation: "spin 1s linear infinite" })} />
								) : (
									<TbUser size={24} className={css({ color: "white" })} />
								)}
							</div>
							<div>
								<div className={toolTitleStyle}>Face Restoration</div>
								<div className={toolDescriptionStyle}>
									{isProcessing && selectedTool === "face-restoration"
										? "Processing..."
										: "Restore and enhance faces using AI"
									}
								</div>
							</div>
						</div>
					</div>

					<div className={toolCardStyle}>
						<div className={hstack({ gap: "0" })}>
							<div className={css({ backgroundColor: "#f59e0b", borderRadius: "8px", padding: "8px", marginRight: "12px" })}>
								<TbDroplet size={24} className={css({ color: "white" })} />
							</div>
							<div>
								<div className={toolTitleStyle}>Remove Watermark</div>
								<div className={toolDescriptionStyle}>Intelligently remove watermarks and text</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ImageProcessingStudio;
