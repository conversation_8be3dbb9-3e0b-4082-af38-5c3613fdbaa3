#!/bin/bash

echo "🚀 Testing Tauri Development Mode"
echo "=================================="
echo ""

# Check if Tauri CLI is installed
if ! command -v cargo tauri &> /dev/null; then
    echo "❌ Tauri CLI not found. Installing..."
    npm install -g @tauri-apps/cli
fi

echo "📦 Building frontend..."
npm run build

echo ""
echo "🔧 Starting Tauri development mode..."
echo "This will open the app in a native window with full functionality."
echo ""
echo "To test background removal:"
echo "1. Upload an image"
echo "2. Click 'Remove Background'"
echo "3. Wait for processing (first time may take longer)"
echo "4. Download the result"
echo ""

# Start Tauri dev mode
npm run tauri dev
