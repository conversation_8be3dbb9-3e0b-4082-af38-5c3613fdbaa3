#!/usr/bin/env python3
"""
Test script to verify the Python backend is working correctly.
"""

import sys
import os
import base64
from pathlib import Path

# Add python-backend to path
sys.path.insert(0, str(Path(__file__).parent / "python-backend"))

try:
    from remove_bg import remove_background_from_base64
    print("✓ Successfully imported remove_bg module")
except ImportError as e:
    print(f"✗ Failed to import remove_bg module: {e}")
    sys.exit(1)

def create_test_image_base64():
    """Create a simple test image in base64 format."""
    # Create a simple 100x100 red square PNG
    from PIL import Image
    import io
    
    # Create a red square
    img = Image.new('RGB', (100, 100), color='red')
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    img_data = buffer.getvalue()
    
    base64_data = base64.b64encode(img_data).decode('utf-8')
    return f"data:image/png;base64,{base64_data}"

def test_background_removal():
    """Test the background removal function."""
    print("Creating test image...")
    test_image = create_test_image_base64()
    print(f"Test image size: {len(test_image)} characters")
    
    print("Testing background removal...")
    result = remove_background_from_base64(test_image)
    
    if result:
        print(f"✓ Background removal successful!")
        print(f"Result size: {len(result)} characters")
        print(f"Result starts with: {result[:50]}...")
        return True
    else:
        print("✗ Background removal failed!")
        return False

if __name__ == "__main__":
    print("Testing Python backend for background removal...")
    print("=" * 50)
    
    # Test if we can import required modules
    try:
        import rembg
        print("✓ rembg module available")
    except ImportError:
        print("✗ rembg module not available")
        sys.exit(1)
    
    try:
        from PIL import Image
        print("✓ PIL module available")
    except ImportError:
        print("✗ PIL module not available")
        sys.exit(1)
    
    # Test background removal
    success = test_background_removal()
    
    if success:
        print("\n✓ All tests passed! Python backend is working correctly.")
        sys.exit(0)
    else:
        print("\n✗ Tests failed! Check the Python backend setup.")
        sys.exit(1)
