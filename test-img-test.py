#!/usr/bin/env python3
"""
Test the remove background function with the specific img_test.jpg file.
"""

import sys
import os
import tempfile
import base64
from pathlib import Path
import subprocess

def image_file_to_base64(image_path):
    """Convert image file to base64 string."""
    with open(image_path, 'rb') as img_file:
        img_data = img_file.read()
        base64_data = base64.b64encode(img_data).decode('utf-8')
        return f"data:image/jpeg;base64,{base64_data}"

def test_with_img_test():
    """Test background removal with img_test.jpg."""
    img_path = Path("public/img_test.jpg")
    
    if not img_path.exists():
        print(f"❌ Test image not found: {img_path}")
        return False
    
    print(f"✅ Found test image: {img_path}")
    print(f"Image size: {img_path.stat().st_size} bytes")
    
    # Convert to base64
    print("Converting image to base64...")
    try:
        test_base64 = image_file_to_base64(img_path)
        print(f"Base64 length: {len(test_base64)} characters")
    except Exception as e:
        print(f"❌ Failed to convert image to base64: {e}")
        return False
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as input_file:
        input_file.write(test_base64)
        input_path = input_file.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as output_file:
        output_path = output_file.name
    
    try:
        # Test the Python script
        python_exe = Path("python-backend/venv/bin/python")
        script_path = Path("python-backend/remove_bg.py")
        
        print(f"Python executable: {python_exe}")
        print(f"Script path: {script_path}")
        print(f"Python exists: {python_exe.exists()}")
        print(f"Script exists: {script_path.exists()}")
        
        if not python_exe.exists() or not script_path.exists():
            print("❌ Python environment or script not found")
            return False
        
        print("\n🔄 Running background removal...")
        print(f"Command: {python_exe} {script_path} --base64 --input {input_path} --output {output_path}")
        
        result = subprocess.run([
            str(python_exe), str(script_path),
            "--base64",
            "--input", input_path,
            "--output", output_path
        ], capture_output=True, text=True, cwd=".", timeout=120)
        
        print(f"\n📊 Results:")
        print(f"Exit code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        if result.stderr:
            print(f"Stderr: {result.stderr}")
        
        if result.returncode == 0:
            # Check output
            if os.path.exists(output_path):
                with open(output_path, 'r') as f:
                    output_content = f.read().strip()
                
                print(f"✅ Output file created")
                print(f"Output size: {len(output_content)} characters")
                print(f"Output starts with: {output_content[:50]}...")
                
                # Save the result for visual inspection
                if output_content.startswith('data:image'):
                    # Extract base64 part and save as image
                    base64_part = output_content.split(',')[1]
                    result_image_data = base64.b64decode(base64_part)
                    
                    result_path = "result_img_test.png"
                    with open(result_path, 'wb') as f:
                        f.write(result_image_data)
                    print(f"✅ Result saved as: {result_path}")
                    print(f"Result image size: {len(result_image_data)} bytes")
                    
                    # Compare sizes
                    original_size = img_path.stat().st_size
                    result_size = len(result_image_data)
                    print(f"📈 Size comparison: {original_size} → {result_size} bytes")
                    
                    return True
                else:
                    print("❌ Output doesn't look like a valid image data URL")
                    return False
            else:
                print("❌ Output file was not created")
                return False
        else:
            print("❌ Script execution failed")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Script execution timed out (>120 seconds)")
        return False
    except Exception as e:
        print(f"❌ Error during execution: {e}")
        return False
    finally:
        # Clean up temp files
        try:
            os.unlink(input_path)
            os.unlink(output_path)
        except:
            pass

if __name__ == "__main__":
    print("🧪 Testing background removal with img_test.jpg")
    print("=" * 60)
    
    success = test_with_img_test()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("Check result_img_test.png to see the background removal result.")
    else:
        print("\n💥 Test failed!")
    
    sys.exit(0 if success else 1)
